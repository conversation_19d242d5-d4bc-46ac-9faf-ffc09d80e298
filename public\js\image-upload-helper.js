/**
 * Image Upload Helper
 * Enhanced functionality for image upload and preview
 */

class ImageUploadHelper {
    constructor() {
        this.validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        this.maxSize = 2 * 1024 * 1024; // 2MB
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupDragAndDrop();
    }

    setupEventListeners() {
        // Handle file input changes
        document.addEventListener('change', (e) => {
            if (e.target.type === 'file' && e.target.accept.includes('image')) {
                this.handleFileChange(e.target);
            }
        });

        // Handle drag and drop
        document.addEventListener('dragover', (e) => {
            if (e.target.closest('.custom-file')) {
                e.preventDefault();
                e.target.closest('.custom-file').classList.add('drag-over');
            }
        });

        document.addEventListener('dragleave', (e) => {
            if (e.target.closest('.custom-file')) {
                e.target.closest('.custom-file').classList.remove('drag-over');
            }
        });

        document.addEventListener('drop', (e) => {
            const customFile = e.target.closest('.custom-file');
            if (customFile) {
                e.preventDefault();
                customFile.classList.remove('drag-over');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const input = customFile.querySelector('input[type="file"]');
                    input.files = files;
                    this.handleFileChange(input);
                }
            }
        });
    }

    setupDragAndDrop() {
        const style = document.createElement('style');
        style.textContent = `
            .custom-file.drag-over {
                border-color: #007bff !important;
                background-color: #e3f2fd !important;
                transform: scale(1.02);
            }
            
            .custom-file.drag-over .custom-file-label {
                border-color: #007bff !important;
                background-color: #e3f2fd !important;
                color: #007bff !important;
            }
        `;
        document.head.appendChild(style);
    }

    handleFileChange(input) {
        const file = input.files[0];
        const container = input.closest('.form-group') || input.closest('.gallery-item');
        
        if (!file) {
            this.clearPreview(container);
            return;
        }

        // Validate file
        if (!this.validateFile(file)) {
            input.value = '';
            this.clearPreview(container);
            return;
        }

        // Update label
        this.updateLabel(input, file.name);
        
        // Show file info
        this.showFileInfo(container, file);
        
        // Show preview
        this.showPreview(container, file);
        
        // Add success animation
        this.addSuccessAnimation(input);
    }

    validateFile(file) {
        if (!this.validTypes.includes(file.type)) {
            this.showError('กรุณาเลือกไฟล์รูปภาพที่ถูกต้อง (JPEG, JPG, PNG, GIF, WebP)');
            return false;
        }

        if (file.size > this.maxSize) {
            this.showError('ขนาดไฟล์ต้องไม่เกิน 2MB');
            return false;
        }

        return true;
    }

    updateLabel(input, fileName) {
        const label = input.nextElementSibling;
        if (label && label.classList.contains('custom-file-label')) {
            label.textContent = fileName;
            label.style.color = '#28a745';
            label.style.fontWeight = '500';
        }
    }

    showFileInfo(container, file) {
        let fileInfo = container.querySelector('.file-info');
        
        if (!fileInfo) {
            fileInfo = document.createElement('div');
            fileInfo.className = 'file-info mt-2';
            fileInfo.style.display = 'none';
            
            const customFile = container.querySelector('.custom-file');
            if (customFile) {
                customFile.parentNode.insertBefore(fileInfo, customFile.nextSibling);
            }
        }

        fileInfo.innerHTML = `
            <div class="alert alert-success py-2">
                <i class="fas fa-check-circle"></i>
                <strong>ไฟล์ที่เลือก:</strong> ${file.name}
                <br>
                <small><strong>ขนาด:</strong> ${this.formatFileSize(file.size)}</small>
                <small class="d-block"><strong>ประเภท:</strong> ${file.type}</small>
            </div>
        `;
        
        fileInfo.style.display = 'block';
    }

    showPreview(container, file) {
        let preview = container.querySelector('.image-preview');
        
        if (!preview) {
            preview = document.createElement('div');
            preview.className = 'image-preview mt-2';
            preview.style.display = 'none';
            
            const fileInfo = container.querySelector('.file-info');
            if (fileInfo) {
                fileInfo.parentNode.insertBefore(preview, fileInfo.nextSibling);
            }
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            preview.innerHTML = `
                <label class="form-label">ตัวอย่างรูปภาพ:</label>
                <div class="border rounded p-2 bg-light text-center">
                    <img src="${e.target.result}" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                </div>
            `;
            preview.style.display = 'block';
            preview.classList.add('fade-in');
        };
        reader.readAsDataURL(file);
    }

    clearPreview(container) {
        const fileInfo = container.querySelector('.file-info');
        const preview = container.querySelector('.image-preview');
        const input = container.querySelector('input[type="file"]');
        const label = container.querySelector('.custom-file-label');

        if (fileInfo) fileInfo.style.display = 'none';
        if (preview) preview.style.display = 'none';
        
        if (label) {
            label.textContent = label.getAttribute('data-original-text') || 'เลือกรูปภาพ...';
            label.style.color = '';
            label.style.fontWeight = '';
        }
    }

    addSuccessAnimation(input) {
        const customFile = input.closest('.custom-file');
        if (customFile) {
            customFile.style.transform = 'scale(0.98)';
            customFile.style.transition = 'transform 0.1s ease';
            
            setTimeout(() => {
                customFile.style.transform = 'scale(1)';
                customFile.style.boxShadow = '0 0 0 3px rgba(40,167,69,0.2)';
                
                setTimeout(() => {
                    customFile.style.boxShadow = '';
                }, 500);
            }, 100);
        }
    }

    showError(message) {
        // Create error toast
        const toast = document.createElement('div');
        toast.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-exclamation-circle"></i> ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        document.body.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }

    showSuccess(message) {
        // Create success toast
        const toast = document.createElement('div');
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        document.body.appendChild(toast);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Static method for external use
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.imageUploadHelper = new ImageUploadHelper();
});

// Export for use in other scripts
window.ImageUploadHelper = ImageUploadHelper;
