# คู่มือการทดสอบการแก้ไขกิจกรรม - SoloShop

## 🎯 วัตถุประสงค์
คู่มือนี้จะช่วยให้คุณทดสอบฟีเจอร์การแก้ไขกิจกรรมที่ได้รับการปรับปรุงแล้ว

## 📋 รายการการทดสอบ

### ✅ การเตรียมตัวก่อนทดสอบ

1. **ตรวจสอบข้อกำหนดเบื้องต้น**
   ```bash
   # ตรวจสอบว่า Laravel server ทำงาน
   php artisan serve
   
   # ตรวจสอบ storage link
   php artisan storage:link
   
   # Clear cache
   php artisan cache:clear
   php artisan config:clear
   ```

2. **เปิด Browser Developer Tools**
   - กด F12 หรือ Ctrl+Shift+I
   - ไปที่แท็บ Console
   - ไปที่แท็บ Network

3. **เข้าสู่ระบบ Admin**
   - ไปที่ `/admin/login`
   - เข้าสู่ระบบด้วยบัญชี admin

### 🔍 การทดสอบขั้นพื้นฐาน

#### 1. ทดสอบการโหลดหน้าแก้ไขกิจกรรม

**ขั้นตอน:**
1. ไปที่ `/admin/activities`
2. คลิกปุ่ม "แก้ไข" ที่กิจกรรมใดกิจกรรมหนึ่ง
3. รอให้หน้าโหลดเสร็จ

**ผลลัพธ์ที่คาดหวัง:**
- หน้าโหลดได้ไม่มี error
- ใน Console ควรเห็นข้อความ:
  ```
  Admin activities edit page loaded
  Bootstrap available: true
  ActivityEditManager initialized
  ```
- ไม่มี error messages สีแดงใน Console

#### 2. ทดสอบการแสดงผลข้อมูล

**ขั้นตอน:**
1. ตรวจสอบว่าข้อมูลกิจกรรมแสดงครบถ้วน
2. ตรวจสอบรูปภาพหน้าปกแสดงได้
3. ตรวจสอบรูปภาพในแกลเลอรี่แสดงได้

**ผลลัพธ์ที่คาดหวัง:**
- ข้อมูลทั้งหมดแสดงถูกต้อง
- รูปภาพโหลดได้ปกติ
- ไม่มี broken images

### 🖼️ การทดสอบการจัดการรูปภาพ

#### 3. ทดสอบการแก้ไขคำบรรยาย

**ขั้นตอน:**
1. คลิกปุ่มดินสอ (🖊️) ที่รูปภาพใดรูปหนึ่ง
2. ตรวจสอบว่า Modal เปิดขึ้น
3. แก้ไขคำบรรยายในช่อง textarea
4. คลิกปุ่ม "บันทึก"
5. ตรวจสอบว่า Modal ปิดและคำบรรยายอัปเดต

**ผลลัพธ์ที่คาดหวัง:**
- Modal เปิดได้ไม่มีปัญหา
- ปุ่มบันทึกแสดง loading state
- คำบรรยายอัปเดตใน UI
- แสดงข้อความ "อัปเดตคำบรรยายสำเร็จ!"

**การแก้ไขปัญหา:**
- หาก Modal ไม่เปิด: ตรวจสอบ Console errors
- หากบันทึกไม่ได้: ตรวจสอบ Network tab

#### 4. ทดสอบการเปลี่ยนรูปภาพ

**ขั้นตอน:**
1. คลิกปุ่มลูกศร (🔄) ที่รูปภาพใดรูปหนึ่ง
2. ตรวจสอบว่า Modal เปิดขึ้น
3. เลือกรูปภาพใหม่จากคอมพิวเตอร์
4. ตรวจสอบว่ามี preview รูปภาพใหม่
5. แก้ไขคำบรรยาย (ถ้าต้องการ)
6. คลิกปุ่ม "เปลี่ยนรูปภาพ"

**ผลลัพธ์ที่คาดหวัง:**
- Modal เปิดได้
- File picker ทำงานได้
- แสดง preview รูปภาพใหม่
- ปุ่มแสดง loading state
- รูปภาพอัปเดตใน UI
- แสดงข้อความ "เปลี่ยนรูปภาพสำเร็จ!"

**ไฟล์ทดสอบที่แนะนำ:**
- JPEG ขนาด < 2MB
- PNG ขนาด < 2MB
- GIF ขนาด < 2MB

#### 5. ทดสอบการลบรูปภาพ

**ขั้นตอน:**
1. คลิกปุ่มถังขยะ (🗑️) ที่รูปภาพใดรูปหนึ่ง
2. ตรวจสอบ confirmation dialog
3. คลิก "ตกลง" เพื่อยืนยันการลบ
4. ตรวจสอบว่ารูปภาพหายไปจาก UI

**ผลลัพธ์ที่คาดหวัง:**
- แสดง confirmation dialog
- ปุ่มแสดง loading state
- รูปภาพหายไปด้วย smooth animation
- แสดงข้อความ "ลบรูปภาพสำเร็จ!"

#### 6. ทดสอบการจัดเรียงรูปภาพ

**ขั้นตอน:**
1. ลากที่ไอคอน grip (≡≡) ของรูปภาพ
2. ลากไปยังตำแหน่งใหม่
3. ปล่อยเมาส์
4. ตรวจสอบว่าลำดับเปลี่ยน

**ผลลัพธ์ที่คาดหวัง:**
- สามารถลากได้
- มี visual feedback ขณะลาก
- ลำดับเปลี่ยนและบันทึกอัตโนมัติ
- หมายเลขลำดับอัปเดต

### 📝 การทดสอบการแก้ไขข้อมูลพื้นฐาน

#### 7. ทดสอบการแก้ไขข้อมูลกิจกรรม

**ขั้นตอน:**
1. แก้ไขชื่อกิจกรรม
2. แก้ไขรายละเอียด
3. เปลี่ยนหมวดหมู่
4. เปลี่ยนวันที่และสถานที่
5. เปลี่ยนสถานะการเผยแพร่
6. คลิกปุ่ม "บันทึกการแก้ไข"

**ผลลัพธ์ที่คาดหวัง:**
- ข้อมูลบันทึกได้
- กลับไปหน้า index
- แสดงข้อความ "อัปเดตกิจกรรมสำเร็จ"

#### 8. ทดสอบการเพิ่มรูปภาพใหม่

**ขั้นตอน:**
1. เลือกรูปภาพในส่วน "เพิ่มรูปภาพใหม่ในแกลเลอรี่"
2. ใส่คำบรรยาย
3. คลิกปุ่ม "เพิ่มรูปภาพ" เพื่อเพิ่มช่องใหม่
4. เลือกรูปภาพเพิ่มเติม
5. คลิกปุ่ม "บันทึกการแก้ไข"

**ผลลัพธ์ที่คาดหวัง:**
- รูปภาพใหม่ถูกเพิ่มเข้าแกลเลอรี่
- คำบรรยายถูกบันทึก
- ลำดับรูปภาพถูกต้อง

### 🚨 การทดสอบ Error Handling

#### 9. ทดสอบไฟล์ไม่ถูกต้อง

**ขั้นตอน:**
1. พยายามอัปโหลดไฟล์ที่ไม่ใช่รูปภาพ (.txt, .pdf)
2. พยายามอัปโหลดรูปภาพขนาดใหญ่ (> 2MB)
3. พยายามอัปโหลดไฟล์เสียหาย

**ผลลัพธ์ที่คาดหวัง:**
- แสดงข้อความ error ที่เหมาะสม
- ไม่ให้อัปโหลดไฟล์ที่ไม่ถูกต้อง
- ระบบไม่ crash

#### 10. ทดสอบการเชื่อมต่อขาด

**ขั้นตอน:**
1. ปิดการเชื่อมต่ออินเทอร์เน็ต
2. พยายามบันทึกข้อมูล
3. เปิดการเชื่อมต่อกลับ

**ผลลัพธ์ที่คาดหวัง:**
- แสดงข้อความ error เมื่อไม่มีการเชื่อมต่อ
- ไม่ทำให้ระบบค้าง
- สามารถทำงานต่อได้เมื่อเชื่อมต่อกลับ

### 📱 การทดสอบ Responsive Design

#### 11. ทดสอบในหน้าจอขนาดต่างๆ

**ขั้นตอน:**
1. ทดสอบในหน้าจอ Desktop (1920x1080)
2. ทดสอบในหน้าจอ Tablet (768x1024)
3. ทดสอบในหน้าจอ Mobile (375x667)

**ผลลัพธ์ที่คาดหวัง:**
- Layout ปรับตัวได้ดีในทุกขนาดหน้าจอ
- ปุ่มและ Modal ใช้งานได้ในมือถือ
- ข้อความอ่านได้ชัดเจน

### 🔧 การแก้ไขปัญหาที่พบบ่อย

#### ปัญหา: Modal ไม่เปิด
**วิธีแก้:**
1. ตรวจสอบ Console errors
2. ตรวจสอบว่า Bootstrap 5 โหลดแล้ว
3. รีเฟรชหน้าเว็บ
4. ตรวจสอบ CSRF token

#### ปัญหา: รูปภาพไม่แสดง
**วิธีแก้:**
1. ตรวจสอบ storage link: `php artisan storage:link`
2. ตรวจสอบ permissions ของโฟลเดอร์ storage
3. ตรวจสอบ path ของรูปภาพ

#### ปัญหา: การอัปโหลดล้มเหลว
**วิธีแก้:**
1. ตรวจสอบขนาดไฟล์ (ต้องไม่เกิน 2MB)
2. ตรวจสอบประเภทไฟล์ (JPEG, PNG, GIF, WebP)
3. ตรวจสอบ Laravel logs: `storage/logs/laravel.log`

#### ปัญหา: JavaScript errors
**วิธีแก้:**
1. ตรวจสอบว่าไฟล์ JS โหลดแล้ว
2. ตรวจสอบ CSRF token
3. Clear browser cache
4. ตรวจสอบ Network requests

### 📊 รายงานผลการทดสอบ

#### Template สำหรับรายงาน:
```
การทดสอบการแก้ไขกิจกรรม - [วันที่]

✅ ผ่าน / ❌ ไม่ผ่าน

1. การโหลดหน้า: ✅/❌
2. การแก้ไขคำบรรยาย: ✅/❌
3. การเปลี่ยนรูปภาพ: ✅/❌
4. การลบรูปภาพ: ✅/❌
5. การจัดเรียงรูปภาพ: ✅/❌
6. การแก้ไขข้อมูลพื้นฐาน: ✅/❌
7. การเพิ่มรูปภาพใหม่: ✅/❌
8. Error handling: ✅/❌
9. Responsive design: ✅/❌

ปัญหาที่พบ:
- [รายละเอียดปัญหา]

ข้อเสนอแนะ:
- [ข้อเสนอแนะสำหรับการปรับปรุง]
```

### 🎉 สรุป

หากการทดสอบทั้งหมดผ่าน แสดงว่าระบบการแก้ไขกิจกรรมทำงานได้ดีและพร้อมใช้งาน หากพบปัญหาใดๆ ให้ดูที่ส่วนการแก้ไขปัญหาหรือตรวจสอบ logs เพื่อหาสาเหตุ
