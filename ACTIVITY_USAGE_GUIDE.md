# คู่มือการใช้งานระบบกิจกรรม - SoloShop

## 🚀 การเริ่มต้นใช้งาน

### 1. เข้าสู่ระบบ Admin
1. เปิดเบราว์เซอร์ไปที่ `http://127.0.0.1:8000/login`
2. ใส่ username และ password ของ admin
3. หลังจาก login สำเร็จ จะถูก redirect ไปหน้า admin dashboard

### 2. เข้าไปยังระบบจัดการกิจกรรม
1. ในหน้า admin dashboard คลิกที่ "จัดการกิจกรรม" หรือ
2. ไปที่ URL: `http://127.0.0.1:8000/admin/activities`

## 📝 การสร้างกิจกรรมใหม่

### ขั้นตอนการสร้าง:
1. **คลิกปุ่ม "เพิ่มกิจกรรมใหม่"** หรือไปที่ `/admin/activities/create`

2. **กรอกข้อมูลพื้นฐาน:**
   - **ชื่อกิจกรรม** (บังคับ): เช่น "งานบุญประจำปี 2567"
   - **รายละเอียดกิจกรรม** (บังคับ): อธิบายรายละเอียดของกิจกรรม
   - **หมวดหมู่** (บังคับ): เลือกจากรายการที่มี
   - **วันที่จัดกิจกรรม** (ไม่บังคับ): เลือกวันที่
   - **สถานที่** (ไม่บังคับ): เช่น "วัดพระแก้ว"

3. **ตั้งสถานะการเผยแพร่:**
   - ✅ เลือก = เผยแพร่ทันที (ผู้เยี่ยมชมเว็บไซต์เห็นได้)
   - ❌ ไม่เลือก = บันทึกเป็นร่าง (ไม่แสดงในเว็บไซต์)

4. **อัปโหลดรูปภาพหน้าปก:**
   - คลิก "เลือกรูปภาพหน้าปก..."
   - เลือกไฟล์รูปภาพ (JPEG, JPG, PNG, GIF, WebP)
   - ขนาดไม่เกิน 2MB
   - จะมี preview แสดงทันที

5. **เพิ่มแกลเลอรี่รูปภาพ:**
   - คลิก "เลือกรูปภาพ..." ในส่วนแกลเลอรี่
   - เลือกรูปภาพ (ขนาดไม่เกิน 2MB ต่อรูป)
   - ใส่คำบรรยายรูปภาพ (ไม่บังคับ)
   - คลิก "เพิ่มรูปภาพอีก" เพื่อเพิ่มรูปเพิ่มเติม
   - สามารถลบรูปภาพได้โดยคลิกปุ่มถังขยะ

6. **บันทึกกิจกรรม:**
   - คลิกปุ่ม "บันทึกกิจกรรม"
   - หากสำเร็จจะแสดงข้อความ "เพิ่มกิจกรรมสำเร็จ"
   - จะถูก redirect ไปหน้ารายการกิจกรรม

## ✏️ การแก้ไขกิจกรรม

### ขั้นตอนการแก้ไข:
1. **เข้าไปหน้ารายการกิจกรรม** `/admin/activities`
2. **คลิกปุ่ม "แก้ไข"** ของกิจกรรมที่ต้องการ
3. **แก้ไขข้อมูล** ตามต้องการ
4. **จัดการรูปภาพ:**
   - เปลี่ยนรูปหน้าปก: อัปโหลดรูปใหม่
   - จัดการแกลเลอรี่:
     - ลากวางเพื่อเรียงลำดับ
     - คลิกรูปเพื่อแก้ไขคำบรรยาย
     - คลิกปุ่มเปลี่ยนรูปเพื่อเปลี่ยนรูปภาพ
     - คลิกปุ่มลบเพื่อลบรูปภาพ
5. **บันทึกการเปลี่ยนแปลง**

## 🗂️ การจัดการหมวดหมู่กิจกรรม

### การสร้างหมวดหมู่ใหม่:
1. ไปที่ `/admin/activity-categories`
2. คลิก "เพิ่มหมวดหมู่ใหม่"
3. กรอกข้อมูล:
   - ชื่อหมวดหมู่
   - คำอธิบาย
   - เลือกสีประจำหมวดหมู่
   - ตั้งสถานะการใช้งาน
4. บันทึก

### การแก้ไขหมวดหมู่:
1. ในหน้ารายการหมวดหมู่ คลิก "แก้ไข"
2. แก้ไขข้อมูลตามต้องการ
3. บันทึกการเปลี่ยนแปลง

## 🖼️ การจัดการรูปภาพขั้นสูง

### ฟีเจอร์ที่มี:
- **Drag & Drop**: ลากวางเพื่อเรียงลำดับรูปภาพ
- **Preview**: ดูตัวอย่างรูปภาพทันทีหลังเลือก
- **Auto Resize**: ระบบจะปรับขนาดรูปภาพอัตโนมัติ
- **Validation**: ตรวจสอบประเภทและขนาดไฟล์
- **Caption**: เพิ่มคำบรรยายสำหรับแต่ละรูป

### ข้อจำกัดของรูปภาพ:
- **ประเภทไฟล์**: JPEG, JPG, PNG, GIF, WebP
- **ขนาดไฟล์**: ไม่เกิน 2MB ต่อรูป
- **ขนาดรูป**: จะถูก resize เป็น 800x600 pixels สูงสุด

## 🔍 การดูและจัดการรายการกิจกรรม

### หน้ารายการกิจกรรม:
- แสดงรูปปก, ชื่อ, หมวดหมู่, วันที่, สถานที่
- แสดงจำนวนรูปภาพในแกลเลอรี่
- แสดงสถานะการเผยแพร่
- ปุ่มจัดการ: แก้ไข, ลบ

### การลบกิจกรรม:
1. คลิกปุ่ม "ลบ" ในรายการกิจกรรม
2. ยืนยันการลบ
3. กิจกรรมและรูปภาพทั้งหมดจะถูกลบ

## 🌐 การดูกิจกรรมในเว็บไซต์

### สำหรับผู้เยี่ยมชม:
- **หน้ารายการกิจกรรม**: `/activities`
- **หน้ารายละเอียดกิจกรรม**: `/activities/{id}`
- **กรองตามหมวดหมู่**: `/activities?category={id}`

### เงื่อนไขการแสดง:
- เฉพาะกิจกรรมที่มีสถานะ "เผยแพร่"
- เรียงลำดับจากใหม่ไปเก่า
- แสดงรูปภาพแกลเลอรี่ทั้งหมด

## ⚠️ ข้อควรระวัง

### การอัปโหลดรูปภาพ:
- ตรวจสอบขนาดไฟล์ก่อนอัปโหลด
- ใช้รูปภาพที่มีคุณภาพดี
- ตั้งชื่อไฟล์ให้เหมาะสม

### การจัดการข้อมูล:
- สำรองข้อมูลก่อนลบกิจกรรม
- ตรวจสอบการเผยแพร่ก่อนบันทึก
- ใส่ข้อมูลให้ครบถ้วน

### ประสิทธิภาพ:
- หลีกเลี่ยงการอัปโหลดรูปภาพขนาดใหญ่เกินไป
- ลบรูปภาพที่ไม่ใช้แล้ว
- ตรวจสอบพื้นที่ storage เป็นระยะ

## 🆘 การแก้ไขปัญหาเบื้องต้น

### รูปภาพไม่แสดง:
```bash
php artisan storage:link
```

### ไม่สามารถอัปโหลดได้:
- ตรวจสอบ permission ของโฟลเดอร์ storage
- ตรวจสอบขนาดไฟล์
- ตรวจสอบประเภทไฟล์

### ข้อผิดพลาดในการบันทึก:
- ตรวจสอบการเชื่อมต่อฐานข้อมูล
- ตรวจสอบ validation errors
- ดู log ใน storage/logs/laravel.log
