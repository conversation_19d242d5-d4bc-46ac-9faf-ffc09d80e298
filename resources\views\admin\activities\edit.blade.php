@extends('layouts.admin')

@section('title', 'แก้ไขกิจกรรม')

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">แก้ไขกิจกรรม</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">หน้าแรก</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.activities.index') }}">จัดการกิจกรรม</a></li>
                        <li class="breadcrumb-item active">แก้ไขกิจกรรม</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">แก้ไขข้อมูลกิจกรรม</h3>
                        </div>
                        <form action="{{ route('admin.activities.update', $activity) }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')
                            <div class="card-body">
                                @if($errors->any())
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="title">ชื่อกิจกรรม <span class="text-danger">*</span></label>
                                            <input type="text"
                                                   name="title"
                                                   id="title"
                                                   class="form-control @error('title') is-invalid @enderror"
                                                   value="{{ old('title', $activity->title) }}"
                                                   placeholder="เช่น งานบุญประจำปี 2567"
                                                   required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="form-group">
                                            <label for="description">รายละเอียดกิจกรรม <span class="text-danger">*</span></label>
                                            <textarea name="description"
                                                      id="description"
                                                      class="form-control @error('description') is-invalid @enderror"
                                                      rows="6"
                                                      placeholder="อธิบายรายละเอียดของกิจกรรม..."
                                                      required>{{ old('description', $activity->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="activity_date">วันที่จัดกิจกรรม</label>
                                                    <input type="date"
                                                           name="activity_date"
                                                           id="activity_date"
                                                           class="form-control @error('activity_date') is-invalid @enderror"
                                                           value="{{ old('activity_date', $activity->activity_date ? $activity->activity_date->format('Y-m-d') : '') }}">
                                                    @error('activity_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="location">สถานที่</label>
                                                    <input type="text"
                                                           name="location"
                                                           id="location"
                                                           class="form-control @error('location') is-invalid @enderror"
                                                           value="{{ old('location', $activity->location) }}"
                                                           placeholder="เช่น วัดพระแก้ว">
                                                    @error('location')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="category_id">หมวดหมู่ <span class="text-danger">*</span></label>
                                            <select name="category_id"
                                                    id="category_id"
                                                    class="form-control @error('category_id') is-invalid @enderror"
                                                    required>
                                                <option value="">เลือกหมวดหมู่</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category->id }}" 
                                                            {{ old('category_id', $activity->category_id) == $category->id ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('category_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">
                                                <a href="{{ route('admin.activity-categories.index') }}" target="_blank">
                                                    จัดการหมวดหมู่
                                                </a>
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox"
                                                       class="custom-control-input"
                                                       id="is_published"
                                                       name="is_published"
                                                       value="1"
                                                       {{ old('is_published', $activity->is_published) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="is_published">เผยแพร่กิจกรรม</label>
                                            </div>
                                            <small class="form-text text-muted">
                                                หากไม่เลือก กิจกรรมจะถูกบันทึกเป็นร่าง
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <!-- Current Cover Image -->
                                @if($activity->cover_image)
                                <div class="form-group">
                                    <label>รูปภาพหน้าปกปัจจุบัน</label>
                                    <div class="mb-2">
                                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($activity->cover_image) }}"
                                             class="img-thumbnail"
                                             style="max-width: 200px;"
                                             alt="รูปภาพหน้าปกปัจจุบัน">
                                    </div>
                                </div>
                                @endif

                                <!-- Cover Image Section -->
                                <div class="form-group">
                                    <label for="cover_image">รูปภาพหน้าปก{{ $activity->cover_image ? ' (เปลี่ยนใหม่)' : '' }}</label>
                                    <div class="custom-file">
                                        <input type="file"
                                               name="cover_image"
                                               id="cover_image"
                                               class="custom-file-input @error('cover_image') is-invalid @enderror"
                                               accept="image/*">
                                        <label class="custom-file-label" for="cover_image">เลือกรูปภาพหน้าปก...</label>
                                        @error('cover_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)
                                    </small>

                                    <!-- แสดงชื่อไฟล์ที่เลือก -->
                                    <div id="cover_file_info" class="mt-2" style="display: none;">
                                        <div class="alert alert-info py-2">
                                            <i class="fas fa-file-image"></i>
                                            <strong>ไฟล์ที่เลือก:</strong> <span id="cover_file_name"></span>
                                            <br>
                                            <small><strong>ขนาด:</strong> <span id="cover_file_size"></span></small>
                                        </div>
                                    </div>

                                    <div id="cover_preview" class="mt-2" style="display: none;">
                                        <label class="form-label">ตัวอย่างรูปภาพ:</label>
                                        <div class="border rounded p-2 bg-light">
                                            <img id="cover_preview_img" src="" class="img-thumbnail d-block mx-auto" style="max-width: 200px;">
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <!-- Current Gallery Images -->
                                @if($activity->images->count() > 0)
                                <div class="form-group">
                                    <label>แกลเลอรี่รูปภาพปัจจุบัน</label>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        คุณสามารถลากเพื่อจัดเรียงลำดับรูปภาพ, แก้ไขคำบรรยาย, เปลี่ยนรูปภาพ หรือลบรูปภาพได้
                                    </div>
                                    <div id="sortable-gallery" class="row" data-activity-id="{{ $activity->id }}">
                                        @foreach($activity->images as $image)
                                        <div class="col-md-4 mb-3 gallery-item-container" data-image-id="{{ $image->id }}">
                                            <div class="card gallery-card">
                                                <div class="card-header p-2 bg-light">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <small class="text-muted">
                                                            <i class="fas fa-grip-vertical drag-handle" style="cursor: move;"></i>
                                                            ลำดับที่ {{ $image->sort_order + 1 }}
                                                        </small>
                                                        <div class="btn-group btn-group-sm">
                                                            <button type="button"
                                                                    class="btn btn-outline-primary edit-caption-btn"
                                                                    data-image-id="{{ $image->id }}"
                                                                    data-current-caption="{{ $image->caption }}"
                                                                    title="แก้ไขคำบรรยาย">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-outline-warning replace-image-btn"
                                                                    data-image-id="{{ $image->id }}"
                                                                    title="เปลี่ยนรูปภาพ">
                                                                <i class="fas fa-exchange-alt"></i>
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-outline-danger delete-image"
                                                                    data-image-id="{{ $image->id }}"
                                                                    title="ลบรูปภาพ">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <img src="{{ \App\Helpers\ImageHelper::getImageUrl($image->image_path) }}"
                                                     class="card-img-top gallery-image"
                                                     style="height: 200px; object-fit: cover;"
                                                     alt="{{ $image->caption }}"
                                                     data-image-id="{{ $image->id }}">
                                                <div class="card-body p-2">
                                                    <div class="caption-display" data-image-id="{{ $image->id }}">
                                                        @if($image->caption)
                                                            <p class="card-text small mb-0">{{ $image->caption }}</p>
                                                        @else
                                                            <p class="card-text small mb-0 text-muted">ไม่มีคำบรรยาย</p>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                                <hr>
                                @endif

                                <!-- Gallery Images Section -->
                                <div class="form-group">
                                    <label>เพิ่มรูปภาพใหม่ในแกลเลอรี่</label>
                                    <div id="gallery_container">
                                        <div class="gallery-item mb-3">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="custom-file">
                                                        <input type="file"
                                                               name="gallery_images[]"
                                                               class="custom-file-input gallery-image"
                                                               accept="image/*">
                                                        <label class="custom-file-label">เลือกรูปภาพ...</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <input type="text"
                                                           name="captions[]"
                                                           class="form-control"
                                                           placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                                                </div>
                                            </div>
                                            <div class="image-preview mt-2" style="display: none;">
                                                <img src="" class="img-thumbnail" style="max-width: 150px;">
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" id="add_gallery_image" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-plus"></i> เพิ่มรูปภาพ
                                    </button>
                                    <small class="form-text text-muted d-block mt-2">
                                        รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB ต่อรูป)
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>บันทึกการแก้ไข
                                </button>
                                <a href="{{ route('admin.activities.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                                </a>
                                <a href="{{ route('activities.show', $activity) }}" class="btn btn-info" target="_blank">
                                    <i class="fas fa-eye me-1"></i>ดูกิจกรรม
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Modal สำหรับแก้ไขคำบรรยาย -->
<div class="modal fade" id="editCaptionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">แก้ไขคำบรรยายรูปภาพ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editCaptionForm">
                    <div class="form-group">
                        <label for="editCaption">คำบรรยาย</label>
                        <textarea id="editCaption" class="form-control" rows="3" placeholder="ใส่คำบรรยายรูปภาพ..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-primary" id="saveCaptionBtn">บันทึก</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal สำหรับเปลี่ยนรูปภาพ -->
<div class="modal fade" id="replaceImageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">เปลี่ยนรูปภาพ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="replaceImageForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="newImage">เลือกรูปภาพใหม่</label>
                        <div class="custom-file">
                            <input type="file" id="newImage" class="custom-file-input" accept="image/*" required>
                            <label class="custom-file-label" for="newImage">เลือกรูปภาพ...</label>
                        </div>
                        <small class="form-text text-muted">รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB)</small>

                        <!-- แสดงข้อมูลไฟล์ที่เลือก -->
                        <div id="replace_file_info" class="mt-2" style="display: none;">
                            <div class="alert alert-success py-2">
                                <i class="fas fa-check-circle"></i>
                                <strong>ไฟล์ที่เลือก:</strong> <span id="replace_file_name"></span>
                                <br>
                                <small><strong>ขนาด:</strong> <span id="replace_file_size"></span></small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="replaceCaption">คำบรรยาย</label>
                        <textarea id="replaceCaption" class="form-control" rows="3" placeholder="ใส่คำบรรยายรูปภาพ..."></textarea>
                    </div>

                    <div id="replacePreview" class="form-group" style="display: none;">
                        <label>ตัวอย่างรูปภาพใหม่</label>
                        <div class="border rounded p-3 bg-light text-center">
                            <img id="replacePreviewImg" src="" class="img-thumbnail" style="max-width: 250px; max-height: 200px;">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-warning" id="replaceImageBtn">เปลี่ยนรูปภาพ</button>
            </div>
        </div>
    </div>
</div>

@push('styles')
<link rel="stylesheet" href="{{ asset('css/admin-activities.css') }}">
<link rel="stylesheet" href="{{ asset('css/admin-image-upload.css') }}">
<link rel="stylesheet" href="{{ asset('css/admin-activities-edit.css') }}">
@endpush

@push('scripts')
<!-- Include SortableJS -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script src="{{ asset('js/image-upload-helper.js') }}"></script>
<script src="{{ asset('js/admin-activities-edit.js') }}"></script>
<script>
// Set global activity ID for the JavaScript class
window.activityId = {{ $activity->id }};

document.addEventListener('DOMContentLoaded', function() {
    let currentImageId = null;

    console.log('Admin activities edit page loaded');
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    console.log('jQuery available:', typeof $ !== 'undefined');

    // Check CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (!csrfToken) {
        console.error('CSRF token not found');
        alert('เกิดข้อผิดพลาด: ไม่พบ CSRF token กรุณารีเฟรชหน้าเว็บ');
        return;
    }

    // Show loading state
    function showLoading(element) {
        if (element) {
            element.disabled = true;
            const originalText = element.innerHTML;
            element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังดำเนินการ...';
            element.dataset.originalText = originalText;
        }
    }

    // Hide loading state
    function hideLoading(element) {
        if (element && element.dataset.originalText) {
            element.disabled = false;
            element.innerHTML = element.dataset.originalText;
            delete element.dataset.originalText;
        }
    }

    // Show error message
    function showErrorMessage(message) {
        // ลบ error toast เก่าถ้ามี
        const existingToast = document.querySelector('.error-toast');
        if (existingToast) {
            existingToast.remove();
        }

        // สร้าง error toast notification
        const toast = document.createElement('div');
        toast.className = 'alert alert-danger alert-dismissible fade show position-fixed error-toast';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);';
        toast.innerHTML = `
            <i class="fas fa-exclamation-circle me-2"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        document.body.appendChild(toast);

        // เพิ่ม animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // ลบ toast หลังจาก 5 วินาที
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }, 5000);

        // Handle manual close
        const closeBtn = toast.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            });
        }
    }
    // Handle cover image preview
    document.getElementById('cover_image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('cover_preview');
        const previewImg = document.getElementById('cover_preview_img');
        const fileInfo = document.getElementById('cover_file_info');
        const fileName = document.getElementById('cover_file_name');
        const fileSize = document.getElementById('cover_file_size');
        const label = document.querySelector('label[for="cover_image"]');

        if (file) {
            // แสดงชื่อไฟล์ใน label
            label.textContent = file.name;

            // แสดงข้อมูลไฟล์
            fileName.textContent = file.name;
            fileSize.textContent = ImageUploadHelper.formatFileSize(file.size);
            fileInfo.style.display = 'block';

            // แสดงตัวอย่างรูปภาพ
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            label.textContent = 'เลือกรูปภาพหน้าปก...';
            preview.style.display = 'none';
            fileInfo.style.display = 'none';
        }
    });

    // Handle gallery images
    let galleryIndex = 1;
    
    document.getElementById('add_gallery_image').addEventListener('click', function() {
        const container = document.getElementById('gallery_container');
        const newItem = document.createElement('div');
        newItem.className = 'gallery-item mb-3';
        newItem.innerHTML = `
            <div class="row">
                <div class="col-md-8">
                    <div class="custom-file">
                        <input type="file"
                               name="gallery_images[]"
                               class="custom-file-input gallery-image"
                               accept="image/*">
                        <label class="custom-file-label">เลือกรูปภาพ...</label>
                    </div>
                </div>
                <div class="col-md-3">
                    <input type="text"
                           name="captions[]"
                           class="form-control"
                           placeholder="คำบรรยายรูปภาพ (ไม่บังคับ)">
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm remove-gallery-item">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="image-preview mt-2" style="display: none;">
                <img src="" class="img-thumbnail" style="max-width: 150px;">
            </div>
        `;
        container.appendChild(newItem);
        galleryIndex++;
    });

    // Handle gallery image preview and removal
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('gallery-image')) {
            const file = e.target.files[0];
            const preview = e.target.closest('.gallery-item').querySelector('.image-preview');
            const previewImg = preview.querySelector('img');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                preview.style.display = 'none';
            }
        }
    });

    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-gallery-item') || e.target.closest('.remove-gallery-item')) {
            e.target.closest('.gallery-item').remove();
        }
    });

    // Update file input labels
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('custom-file-input')) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'เลือกรูปภาพ...';
            const label = e.target.nextElementSibling;
            label.textContent = fileName;
        }
    });

    // Initialize Sortable for gallery
    const sortableGallery = document.getElementById('sortable-gallery');
    if (sortableGallery) {
        new Sortable(sortableGallery, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            handle: '.drag-handle',
            onEnd: function(evt) {
                const imageIds = Array.from(sortableGallery.children).map(item =>
                    item.getAttribute('data-image-id')
                );

                // Update sort order on server
                fetch(`{{ route('admin.activities.images.update-order', $activity->id) }}`, {
                    method: 'PUT',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        image_ids: imageIds
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update order numbers in UI
                        sortableGallery.querySelectorAll('.gallery-item-container').forEach((item, index) => {
                            const orderText = item.querySelector('.drag-handle').parentNode;
                            orderText.innerHTML = `<i class="fas fa-grip-vertical drag-handle" style="cursor: move;"></i> ลำดับที่ ${index + 1}`;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }
        });
    }

    // Handle edit caption
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('edit-caption-btn') || e.target.closest('.edit-caption-btn')) {
            e.preventDefault();
            e.stopPropagation();

            const button = e.target.classList.contains('edit-caption-btn') ? e.target : e.target.closest('.edit-caption-btn');
            currentImageId = button.getAttribute('data-image-id');
            const currentCaption = button.getAttribute('data-current-caption') || '';

            console.log('Edit caption clicked for image ID:', currentImageId);
            console.log('Current caption:', currentCaption);

            document.getElementById('editCaption').value = currentCaption;

            // ใช้ Bootstrap 5 modal API
            try {
                const modal = document.getElementById('editCaptionModal');
                if (modal) {
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();
                } else {
                    console.error('Modal element not found');
                    alert('เกิดข้อผิดพลาดในการเปิด Modal');
                }
            } catch (error) {
                console.error('Error opening modal:', error);
                alert('เกิดข้อผิดพลาดในการเปิด Modal');
            }
        }
    });

    // Save caption
    document.getElementById('saveCaptionBtn').addEventListener('click', function() {
        const saveBtn = this;
        const newCaption = document.getElementById('editCaption').value;

        if (!currentImageId) {
            alert('ไม่พบข้อมูลรูปภาพ');
            return;
        }

        showLoading(saveBtn);

        fetch(`{{ route('admin.activities.images.update-caption', 'PLACEHOLDER') }}`.replace('PLACEHOLDER', currentImageId), {
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                caption: newCaption
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading(saveBtn);

            if (data.success) {
                // Update caption in UI
                const captionDisplay = document.querySelector(`.caption-display[data-image-id="${currentImageId}"] p`);
                if (captionDisplay) {
                    if (newCaption.trim()) {
                        captionDisplay.textContent = newCaption;
                        captionDisplay.classList.remove('text-muted');
                    } else {
                        captionDisplay.textContent = 'ไม่มีคำบรรยาย';
                        captionDisplay.classList.add('text-muted');
                    }
                }

                // Update button data
                const editBtn = document.querySelector(`.edit-caption-btn[data-image-id="${currentImageId}"]`);
                if (editBtn) {
                    editBtn.setAttribute('data-current-caption', newCaption);
                }

                // ปิด modal
                closeModal('editCaptionModal');

                // แสดงข้อความสำเร็จ
                showSuccessMessage('อัปเดตคำบรรยายสำเร็จ!');
            } else {
                alert(data.message || 'เกิดข้อผิดพลาดในการบันทึกคำบรรยาย');
            }
        })
        .catch(error => {
            hideLoading(saveBtn);
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการบันทึกคำบรรยาย: ' + error.message);
        });
    });

    // Handle replace image
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('replace-image-btn') || e.target.closest('.replace-image-btn')) {
            const button = e.target.classList.contains('replace-image-btn') ? e.target : e.target.closest('.replace-image-btn');
            currentImageId = button.getAttribute('data-image-id');

            // Get current caption
            const currentCaption = document.querySelector(`.edit-caption-btn[data-image-id="${currentImageId}"]`).getAttribute('data-current-caption') || '';
            document.getElementById('replaceCaption').value = currentCaption;

            // แสดง modal
            const modal = document.getElementById('replaceImageModal');
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    });

    // Handle new image preview for replace
    document.getElementById('newImage').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('replacePreview');
        const previewImg = document.getElementById('replacePreviewImg');
        const fileInfo = document.getElementById('replace_file_info');
        const fileName = document.getElementById('replace_file_name');
        const fileSize = document.getElementById('replace_file_size');
        const label = document.querySelector('label[for="newImage"]');

        if (file) {
            // ตรวจสอบประเภทไฟล์
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!validTypes.includes(file.type)) {
                alert('กรุณาเลือกไฟล์รูปภาพที่ถูกต้อง (JPEG, JPG, PNG, GIF, WebP)');
                e.target.value = '';
                return;
            }

            // ตรวจสอบขนาดไฟล์ (2MB)
            if (file.size > 2 * 1024 * 1024) {
                alert('ขนาดไฟล์ต้องไม่เกิน 2MB');
                e.target.value = '';
                return;
            }

            // แสดงชื่อไฟล์ใน label
            label.textContent = file.name;

            // แสดงข้อมูลไฟล์
            fileName.textContent = file.name;
            fileSize.textContent = ImageUploadHelper.formatFileSize(file.size);
            fileInfo.style.display = 'block';

            // แสดงตัวอย่างรูปภาพ
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            label.textContent = 'เลือกรูปภาพ...';
            preview.style.display = 'none';
            fileInfo.style.display = 'none';
        }
    });

    // Replace image
    document.getElementById('replaceImageBtn').addEventListener('click', function() {
        const replaceBtn = this;
        const fileInput = document.getElementById('newImage');
        const caption = document.getElementById('replaceCaption').value;

        if (!currentImageId) {
            alert('ไม่พบข้อมูลรูปภาพ');
            return;
        }

        if (!fileInput.files[0]) {
            alert('กรุณาเลือกรูปภาพใหม่');
            return;
        }

        showLoading(replaceBtn);

        const formData = new FormData();
        formData.append('new_image', fileInput.files[0]);
        formData.append('caption', caption);

        fetch(`{{ route('admin.activities.images.replace', 'PLACEHOLDER') }}`.replace('PLACEHOLDER', currentImageId), {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading(replaceBtn);

            if (data.success) {
                // Update image in UI
                const galleryImage = document.querySelector(`.gallery-image[data-image-id="${currentImageId}"]`);
                if (galleryImage && data.new_image_url) {
                    galleryImage.src = data.new_image_url;
                }

                // Update caption
                const captionDisplay = document.querySelector(`.caption-display[data-image-id="${currentImageId}"] p`);
                if (captionDisplay) {
                    if (caption.trim()) {
                        captionDisplay.textContent = caption;
                        captionDisplay.classList.remove('text-muted');
                    } else {
                        captionDisplay.textContent = 'ไม่มีคำบรรยาย';
                        captionDisplay.classList.add('text-muted');
                    }
                }

                // Update button data
                const editBtn = document.querySelector(`.edit-caption-btn[data-image-id="${currentImageId}"]`);
                if (editBtn) {
                    editBtn.setAttribute('data-current-caption', caption);
                }

                // ปิด modal
                closeModal('replaceImageModal');

                // Reset form
                document.getElementById('replaceImageForm').reset();
                document.getElementById('replacePreview').style.display = 'none';
                document.getElementById('replace_file_info').style.display = 'none';
                document.querySelector('label[for="newImage"]').textContent = 'เลือกรูปภาพ...';

                // แสดงข้อความสำเร็จ
                showSuccessMessage('เปลี่ยนรูปภาพสำเร็จ!');
            } else {
                alert(data.message || 'เกิดข้อผิดพลาดในการเปลี่ยนรูปภาพ');
            }
        })
        .catch(error => {
            hideLoading(replaceBtn);
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการเปลี่ยนรูปภาพ: ' + error.message);
        });
    });

    // Handle delete existing images
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-image') || e.target.closest('.delete-image')) {
            const button = e.target.classList.contains('delete-image') ? e.target : e.target.closest('.delete-image');
            const imageId = button.getAttribute('data-image-id');

            if (!imageId) {
                alert('ไม่พบข้อมูลรูปภาพ');
                return;
            }

            if (confirm('คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้? การดำเนินการนี้ไม่สามารถยกเลิกได้')) {
                showLoading(button);

                fetch(`{{ route('admin.activities.images.delete', ['activity' => $activity->id, 'image' => 'PLACEHOLDER']) }}`.replace('PLACEHOLDER', imageId), {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    hideLoading(button);

                    if (data.success) {
                        const container = button.closest('.gallery-item-container');
                        if (container) {
                            container.style.transition = 'opacity 0.3s ease';
                            container.style.opacity = '0';
                            setTimeout(() => {
                                container.remove();
                                showSuccessMessage('ลบรูปภาพสำเร็จ!');
                            }, 300);
                        }
                    } else {
                        alert(data.message || 'เกิดข้อผิดพลาดในการลบรูปภาพ');
                    }
                })
                .catch(error => {
                    hideLoading(button);
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการลบรูปภาพ: ' + error.message);
                });
            }
        }
    });

    // Handle modal close buttons (Bootstrap 5 ใช้ data-bs-dismiss)
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('btn-close') || e.target.closest('.btn-close')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                closeModal(modal.id);
            }
        }

        if (e.target.getAttribute('data-bs-dismiss') === 'modal') {
            const modal = e.target.closest('.modal');
            if (modal) {
                closeModal(modal.id);
            }
        }
    });

    // Handle backdrop clicks
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target.id);
        }
    });

    // Handle ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal.show');
            openModals.forEach(modal => {
                closeModal(modal.id);
            });
        }
    });

    // ฟังก์ชั่นช่วยเหลือ
    function closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        // ใช้ Bootstrap 5 API
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        } else {
            // สร้าง instance ใหม่แล้วปิด
            const newModal = new bootstrap.Modal(modal);
            newModal.hide();
        }
    }

    function showSuccessMessage(message) {
        // ลบ toast เก่าถ้ามี
        const existingToast = document.querySelector('.success-toast');
        if (existingToast) {
            existingToast.remove();
        }

        // สร้าง toast notification
        const toast = document.createElement('div');
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed success-toast';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);';
        toast.innerHTML = `
            <i class="fas fa-check-circle me-2"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        document.body.appendChild(toast);

        // เพิ่ม animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // ลบ toast หลังจาก 4 วินาที
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }, 4000);

        // Handle manual close
        const closeBtn = toast.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            });
        }
    }

    // Initialize sortable gallery
    if (typeof Sortable !== 'undefined') {
        const galleryContainer = document.getElementById('sortable-gallery');
        if (galleryContainer) {
            new Sortable(galleryContainer, {
                animation: 150,
                handle: '.drag-handle',
                onEnd: function(evt) {
                    updateGalleryOrder();
                }
            });
        }
    }
});
</script>
@endpush
@endsection
