

<?php $__env->startSection('title', 'แดชบอร์ด - Admin Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-tachometer-alt me-2"></i>แดชบอร์ด
                    </h1>
                    <p class="text-muted">ภาพรวมระบบจัดการเว็บไซต์ SoloShop</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item active">แดชบอร์ด</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row">
                <!-- Services Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <?php
                                try {
                                    $servicesCount = \App\Models\Service::count();
                                } catch (\Exception $e) {
                                    $servicesCount = 0;
                                }
                            ?>
                            <h3><?php echo e(number_format($servicesCount)); ?></h3>
                            <p>บริการทั้งหมด</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <a href="<?php echo e(route('admin.services.index')); ?>" class="small-box-footer">
                            ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Packages Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <?php
                                try {
                                    $packagesCount = \App\Models\Package::count();
                                } catch (\Exception $e) {
                                    $packagesCount = 0;
                                }
                            ?>
                            <h3><?php echo e(number_format($packagesCount)); ?></h3>
                            <p>แพ็กเกจทั้งหมด</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <a href="<?php echo e(route('admin.packages.index')); ?>" class="small-box-footer">
                            ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Activities Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <?php
                                try {
                                    $activitiesCount = \App\Models\Activity::count();
                                } catch (\Exception $e) {
                                    $activitiesCount = 0;
                                }
                            ?>
                            <h3><?php echo e(number_format($activitiesCount)); ?></h3>
                            <p>กิจกรรมทั้งหมด</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-images"></i>
                        </div>
                        <a href="<?php echo e(route('admin.activities.index')); ?>" class="small-box-footer">
                            ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Contacts Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <?php
                                try {
                                    $unreadContactsCount = \App\Models\Contact::where('is_read', false)->count();
                                    $totalContactsCount = \App\Models\Contact::count();
                                } catch (\Exception $e) {
                                    $unreadContactsCount = 0;
                                    $totalContactsCount = 0;
                                }
                            ?>
                            <h3><?php echo e(number_format($unreadContactsCount)); ?></h3>
                            <p>ข้อความใหม่ (<?php echo e(number_format($totalContactsCount)); ?> ทั้งหมด)</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <a href="<?php echo e(route('admin.contacts.index')); ?>" class="small-box-footer">
                            ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>


            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-bolt me-2"></i>การดำเนินการด่วน
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- เพิ่มเนื้อหา -->
                                <div class="col-lg-6 mb-4">
                                    <h5 class="text-muted mb-3">
                                        <i class="fas fa-plus-circle me-2"></i>เพิ่มเนื้อหาใหม่
                                    </h5>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary btn-block">
                                                <i class="fas fa-tools me-2"></i>เพิ่มบริการ
                                            </a>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <a href="<?php echo e(route('admin.packages.create')); ?>" class="btn btn-success btn-block">
                                                <i class="fas fa-box me-2"></i>เพิ่มแพ็กเกจ
                                            </a>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <a href="<?php echo e(route('admin.activities.create')); ?>" class="btn btn-info btn-block">
                                                <i class="fas fa-images me-2"></i>เพิ่มกิจกรรม
                                            </a>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <a href="<?php echo e(route('admin.activity-categories.create')); ?>" class="btn btn-outline-info btn-block">
                                                <i class="fas fa-tags me-2"></i>เพิ่มหมวดหมู่
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- จัดการและตั้งค่า -->
                                <div class="col-lg-6 mb-4">
                                    <h5 class="text-muted mb-3">
                                        <i class="fas fa-cogs me-2"></i>จัดการและตั้งค่า
                                    </h5>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <a href="<?php echo e(route('admin.homepage.index')); ?>" class="btn btn-warning btn-block">
                                                <i class="fas fa-home me-2"></i>แก้ไขหน้าแรก
                                            </a>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <a href="<?php echo e(route('admin.settings.index')); ?>" class="btn btn-secondary btn-block">
                                                <i class="fas fa-cog me-2"></i>ตั้งค่าเว็บไซต์
                                            </a>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <a href="<?php echo e(route('admin.contacts.index')); ?>" class="btn btn-outline-danger btn-block">
                                                <i class="fas fa-envelope me-2"></i>ข้อความติดต่อ
                                                <?php if($unreadContactsCount > 0): ?>
                                                    <span class="badge badge-danger ml-1"><?php echo e($unreadContactsCount); ?></span>
                                                <?php endif; ?>
                                            </a>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <a href="/" target="_blank" class="btn btn-outline-success btn-block">
                                                <i class="fas fa-external-link-alt me-2"></i>ดูเว็บไซต์
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Recent Activities -->
            <div class="row mt-4">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-list me-2"></i>บริการล่าสุด
                            </h3>
                        </div>
                        <div class="card-body">
                            <?php
                                try {
                                    $recentServices = \App\Models\Service::latest()->take(5)->get();
                                } catch (\Exception $e) {
                                    $recentServices = collect();
                                }
                            ?>
                            <?php if($recentServices->count() > 0): ?>
                                <div class="list-group list-group-flush">
                                    <?php $__currentLoopData = $recentServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?php echo e($service->title); ?></h6>
                                                <small class="text-muted"><?php echo e(Str::limit($service->description, 50)); ?></small>
                                            </div>
                                            <a href="<?php echo e(route('admin.services.edit', $service)); ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted text-center">ยังไม่มีบริการ</p>
                            <?php endif; ?>
                </div>
            </div>
        </div>

                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-envelope me-2"></i>ข้อความติดต่อล่าสุด
                            </h3>
                        </div>
                        <div class="card-body">
                            <?php
                                try {
                                    $recentContacts = \App\Models\Contact::latest()->take(5)->get();
                                } catch (\Exception $e) {
                                    $recentContacts = collect();
                                }
                            ?>
                            <?php if($recentContacts->count() > 0): ?>
                                <div class="list-group list-group-flush">
                                    <?php $__currentLoopData = $recentContacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php echo e($contact->name); ?>

                                                    <?php if(isset($contact->is_read) && !$contact->is_read): ?>
                                                        <span class="badge badge-danger">ใหม่</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <small class="text-muted"><?php echo e($contact->email); ?> - <?php echo e(Str::limit($contact->message, 50)); ?></small>
                                                <br><small class="text-muted"><?php echo e($contact->created_at->diffForHumans()); ?></small>
                                            </div>
                                            <a href="<?php echo e(route('admin.contacts.edit', $contact)); ?>" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted text-center">ยังไม่มีข้อความติดต่อ</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>