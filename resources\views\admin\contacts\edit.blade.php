@extends('layouts.admin')

@section('title', 'ดูรายละเอียดข้อความติดต่อ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-envelope-open me-2"></i>รายละเอียดข้อความติดต่อ
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.contacts.index') }}">จัดการข้อความติดต่อ</a></li>
                        <li class="breadcrumb-item active">รายละเอียด</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                ข้อความจาก: {{ $contact->name }}
                                @if(!$contact->is_read)
                                    <span class="badge badge-warning ml-2">ยังไม่อ่าน</span>
                                @else
                                    <span class="badge badge-success ml-2">อ่านแล้ว</span>
                                @endif
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-user text-primary"></i> ข้อมูลผู้ติดต่อ</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>ชื่อ:</strong></td>
                                            <td>{{ $contact->name }}</td>
                                        </tr>
                                        @if($contact->phone)
                                        <tr>
                                            <td><strong>เบอร์โทร:</strong></td>
                                            <td>
                                                <a href="tel:{{ $contact->phone }}" class="text-decoration-none">
                                                    <i class="fas fa-phone text-success"></i> {{ $contact->phone }}
                                                </a>
                                            </td>
                                        </tr>
                                        @endif
                                        @if($contact->email)
                                        <tr>
                                            <td><strong>อีเมล:</strong></td>
                                            <td>
                                                <a href="mailto:{{ $contact->email }}" class="text-decoration-none">
                                                    <i class="fas fa-envelope text-info"></i> {{ $contact->email }}
                                                </a>
                                            </td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-clock text-warning"></i> ข้อมูลเวลา</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>ส่งเมื่อ:</strong></td>
                                            <td>{{ $contact->created_at->format('d/m/Y H:i:s') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>เวลาที่ผ่านมา:</strong></td>
                                            <td>{{ $contact->created_at->diffForHumans() }}</td>
                                        </tr>
                                        @if($contact->updated_at != $contact->created_at)
                                        <tr>
                                            <td><strong>อัปเดตล่าสุด:</strong></td>
                                            <td>{{ $contact->updated_at->format('d/m/Y H:i:s') }}</td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>

                            <hr>

                            <h6><i class="fas fa-comment text-success"></i> ข้อความ</h6>
                            <div class="bg-light p-3 rounded">
                                <p class="mb-0">{{ $contact->message ?: 'ไม่มีข้อความ' }}</p>
                            </div>
                        </div>
                        <div class="card-footer">
                            @if(!$contact->is_read)
                                <form action="{{ route('admin.contacts.update', $contact) }}" method="POST" style="display:inline;">
                                    @csrf @method('PUT')
                                    <input type="hidden" name="is_read" value="1">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-check me-1"></i>ทำเครื่องหมายว่าอ่านแล้ว
                                    </button>
                                </form>
                            @endif
                            <a href="{{ route('admin.contacts.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                            </a>
                            <form action="{{ route('admin.contacts.destroy', $contact) }}" method="POST" style="display:inline;" onsubmit="return confirm('ยืนยันการลบข้อความนี้?')">
                                @csrf @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i>ลบข้อความ
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">การดำเนินการ</h3>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                @if($contact->phone)
                                <a href="tel:{{ $contact->phone }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-phone text-success"></i> โทรหา {{ $contact->name }}
                                </a>
                                @endif
                                @if($contact->email)
                                <a href="mailto:{{ $contact->email }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-envelope text-info"></i> ส่งอีเมลถึง {{ $contact->name }}
                                </a>
                                @endif
                                <a href="{{ route('admin.contacts.index') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-list text-primary"></i> ดูข้อความทั้งหมด
                                </a>
                            </div>
                        </div>
                    </div>

                    @if($contact->phone || $contact->email)
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">ข้อมูลติดต่อ</h3>
                        </div>
                        <div class="card-body">
                            <div class="text-center">
                                <h5>{{ $contact->name }}</h5>
                                @if($contact->phone)
                                    <p class="mb-1">
                                        <i class="fas fa-phone text-success"></i> {{ $contact->phone }}
                                    </p>
                                @endif
                                @if($contact->email)
                                    <p class="mb-0">
                                        <i class="fas fa-envelope text-info"></i> {{ $contact->email }}
                                    </p>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
</div>
@endsection