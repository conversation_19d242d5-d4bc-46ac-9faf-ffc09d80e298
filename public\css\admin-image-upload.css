/**
 * Admin Image Upload Styles
 * Enhanced styles for image upload functionality
 */

/* File Upload Enhancements */
.custom-file {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.custom-file:hover {
    box-shadow: 0 2px 8px rgba(0,123,255,0.2);
}

.custom-file-input:focus ~ .custom-file-label {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.custom-file-label {
    border: 2px dashed #dee2e6;
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 12px 16px;
    border-radius: 8px;
}

.custom-file-label:hover {
    border-color: #007bff;
    background-color: #e3f2fd;
    color: #007bff;
}

.custom-file-label::after {
    content: "เลือกไฟล์";
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.custom-file-label:hover::after {
    background-color: #0056b3;
}

/* File Info Display */
.file-info {
    animation: slideDown 0.3s ease;
}

.file-info .alert {
    border-left: 4px solid #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-radius: 8px;
}

.file-info .fas {
    color: #28a745;
    margin-right: 8px;
}

/* Image Preview Enhancements */
.image-preview {
    animation: fadeInUp 0.4s ease;
    margin-top: 15px;
}

.image-preview img {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.image-preview img:hover {
    transform: scale(1.05);
}

.image-preview .border {
    border: 2px solid #e9ecef !important;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Gallery Items */
.gallery-item {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.gallery-item:hover {
    border-color: #007bff;
    box-shadow: 0 8px 25px rgba(0,123,255,0.15);
    transform: translateY(-2px);
}

.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007bff, #28a745);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover::before {
    opacity: 1;
}

/* Gallery Cards */
.gallery-card {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.gallery-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.15);
}

.gallery-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.gallery-card .card-img-top {
    transition: transform 0.3s ease;
}

.gallery-card:hover .card-img-top {
    transform: scale(1.02);
}

/* Button Enhancements */
.btn-group-sm .btn {
    border-radius: 6px;
    margin: 0 2px;
    transition: all 0.2s ease;
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

.btn-outline-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255,193,7,0.3);
}

.btn-outline-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220,53,69,0.3);
}

/* Drag Handle */
.drag-handle {
    color: #6c757d;
    cursor: move;
    transition: color 0.2s ease;
}

.drag-handle:hover {
    color: #007bff;
}

/* Sortable States */
.sortable-ghost {
    opacity: 0.5;
    transform: scale(0.95);
}

.sortable-chosen {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 3px rgba(40,167,69,0.2);
}

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* Toast Notifications */
.alert.position-fixed {
    border-radius: 12px;
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
}

.alert-success.position-fixed {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-left: 4px solid #28a745;
}

/* Responsive Design */
@media (max-width: 768px) {
    .gallery-item .row {
        flex-direction: column;
    }
    
    .gallery-item .col-md-6,
    .gallery-item .col-md-5,
    .gallery-item .col-md-1 {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .btn-group-sm {
        justify-content: center;
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
}

.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #007bff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
