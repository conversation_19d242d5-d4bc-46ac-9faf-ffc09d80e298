@extends('layouts.admin')

@section('title', 'จัดการข้อความติดต่อ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-envelope me-2"></i>จัดการข้อความติดต่อ
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item active">จัดการข้อความติดต่อ</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">รายการข้อความติดต่อทั้งหมด</h3>
                            <div class="card-tools">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown">
                                        <i class="fas fa-filter me-1"></i>กรองข้อมูล
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="{{ route('admin.contacts.index') }}">ทั้งหมด</a>
                                        <a class="dropdown-item" href="{{ route('admin.contacts.index', ['filter' => 'unread']) }}">ยังไม่อ่าน</a>
                                        <a class="dropdown-item" href="{{ route('admin.contacts.index', ['filter' => 'read']) }}">อ่านแล้ว</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible">
                                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                                    {{ session('success') }}
                                </div>
                            @endif

                            @if($contacts->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th style="width: 40px;">สถานะ</th>
                                                <th>ชื่อ</th>
                                                <th>ติดต่อ</th>
                                                <th>ข้อความ</th>
                                                <th style="width: 120px;">วันที่</th>
                                                <th style="width: 150px;">จัดการ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($contacts as $contact)
                                                <tr class="{{ !$contact->is_read ? 'table-warning' : '' }}">
                                                    <td class="text-center">
                                                        @if($contact->is_read)
                                                            <i class="fas fa-envelope-open text-success" title="อ่านแล้ว"></i>
                                                        @else
                                                            <i class="fas fa-envelope text-warning" title="ยังไม่อ่าน"></i>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <strong>{{ $contact->name }}</strong>
                                                        @if(!$contact->is_read)
                                                            <span class="badge badge-warning ml-1">ใหม่</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($contact->phone)
                                                            <div><i class="fas fa-phone text-primary"></i> {{ $contact->phone }}</div>
                                                        @endif
                                                        @if($contact->email)
                                                            <div><i class="fas fa-envelope text-info"></i> {{ $contact->email }}</div>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        {{ Str::limit($contact->message, 60) }}
                                                    </td>
                                                    <td class="text-center">
                                                        <small>{{ $contact->created_at->format('d/m/Y') }}</small><br>
                                                        <small class="text-muted">{{ $contact->created_at->format('H:i') }}</small>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('admin.contacts.edit', $contact) }}"
                                                               class="btn btn-sm btn-info"
                                                               title="ดูรายละเอียด">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            @if(!$contact->is_read)
                                                                <form action="{{ route('admin.contacts.update', $contact) }}"
                                                                      method="POST"
                                                                      style="display:inline;">
                                                                    @csrf @method('PUT')
                                                                    <input type="hidden" name="is_read" value="1">
                                                                    <button type="submit" class="btn btn-sm btn-success" title="ทำเครื่องหมายว่าอ่านแล้ว">
                                                                        <i class="fas fa-check"></i>
                                                                    </button>
                                                                </form>
                                                            @endif
                                                            <form action="{{ route('admin.contacts.destroy', $contact) }}"
                                                                  method="POST"
                                                                  style="display:inline;"
                                                                  onsubmit="return confirm('ยืนยันการลบข้อความจาก {{ $contact->name }}?')">
                                                                @csrf @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-danger" title="ลบ">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">ยังไม่มีข้อความติดต่อ</h5>
                                    <p class="text-muted">เมื่อมีผู้ติดต่อเข้ามา ข้อความจะแสดงที่นี่</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection