@extends('layouts.admin')

@section('title', 'จัดการกิจกรรม')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/admin-activities.css') }}">
@endpush

@section('content')
<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">จัดการกิจกรรม</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">หน้าแรก</a></li>
                        <li class="breadcrumb-item active">จัดการกิจกรรม</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">รายการกิจกรรม</h3>
                            <div class="card-tools">
                                <a href="{{ route('admin.activities.create') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
                                </a>
                                <a href="{{ route('admin.activity-categories.index') }}" class="btn btn-info btn-sm">
                                    <i class="fas fa-tags"></i> จัดการหมวดหมู่
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session('success') }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            @endif

                            @if(session('error'))
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    {{ session('error') }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            @endif

                            @if($activities->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th style="width: 60px;">รูป</th>
                                                <th>ชื่อกิจกรรม</th>
                                                <th style="width: 120px;">หมวดหมู่</th>
                                                <th style="width: 100px;">วันที่จัด</th>
                                                <th style="width: 80px;">สถานะ</th>
                                                <th style="width: 150px;">จัดการ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($activities as $activity)
                                                <tr>
                                                    <td class="text-center">
                                                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($activity->cover_image) }}"
                                                             class="img-thumbnail"
                                                             style="max-width: 45px; max-height: 45px; cursor: pointer;"
                                                             alt="รูปปกกิจกรรม"
                                                             title="คลิกเพื่อดูรูปใหญ่"
                                                             onclick="showImageModal('{{ \App\Helpers\ImageHelper::getImageUrl($activity->cover_image) }}', '{{ $activity->title }}')">
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <strong>{{ $activity->title }}</strong>
                                                            @if($activity->images->count() > 0)
                                                                <span class="badge badge-primary badge-sm ml-2" title="{{ $activity->images->count() }} รูปภาพในแกลเลอรี่">
                                                                    <i class="fas fa-images"></i> {{ $activity->images->count() }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                        <small class="text-muted">{{ Str::limit($activity->description, 60) }}</small>
                                                        @if($activity->location)
                                                            <br><small class="text-info"><i class="fas fa-map-marker-alt"></i> {{ Str::limit($activity->location, 30) }}</small>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <span class="badge" style="background-color: {{ $activity->category->color }}">
                                                            {{ $activity->category->name }}
                                                        </span>
                                                    </td>
                                                    <td class="text-center">
                                                        @if($activity->activity_date)
                                                            <span class="badge badge-info">
                                                                {{ $activity->activity_date->format('d/m/Y') }}
                                                            </span>
                                                        @else
                                                            <span class="text-muted">-</span>
                                                        @endif
                                                    </td>
                                                    <td class="text-center">
                                                        @if($activity->is_published)
                                                            <span class="badge badge-success">เผยแพร่</span>
                                                        @else
                                                            <span class="badge badge-warning">ร่าง</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('activities.show', $activity) }}" 
                                                               class="btn btn-info btn-sm" target="_blank" title="ดู">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="{{ route('admin.activities.edit', $activity) }}" 
                                                               class="btn btn-warning btn-sm" title="แก้ไข">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <form action="{{ route('admin.activities.destroy', $activity) }}" 
                                                                  method="POST" class="d-inline"
                                                                  onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบกิจกรรมนี้?')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-danger btn-sm" title="ลบ">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">ยังไม่มีกิจกรรม</h5>
                                    <p class="text-muted">เริ่มต้นสร้างกิจกรรมแรกของคุณ</p>
                                    <a href="{{ route('admin.activities.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>เพิ่มกิจกรรมใหม่
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">รูปภาพกิจกรรม</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="" style="max-height: 500px;">
                <p id="modalCaption" class="mt-3 text-muted"></p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function showImageModal(imageSrc, caption) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('modalCaption').textContent = caption;
    $('#imageModal').modal('show');
}
</script>
@endpush

@endsection
