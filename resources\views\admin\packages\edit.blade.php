@extends('layouts.admin')

@section('title', 'แก้ไขแพ็กเกจ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2"></i>แก้ไขแพ็กเกจ
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.packages.index') }}">จัดการแพ็กเกจ</a></li>
                        <li class="breadcrumb-item active">แก้ไขแพ็กเกจ</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">แก้ไขข้อมูลแพ็กเกจ: {{ $package->name }}</h3>
                        </div>
                        <form action="{{ route('admin.packages.update', $package) }}" method="POST" enctype="multipart/form-data">
                            @csrf @method('PUT')
                            <div class="card-body">
                                @if($errors->any())
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="form-group">
                                    <label for="name">ชื่อแพ็กเกจ <span class="text-danger">*</span></label>
                                    <input type="text"
                                           name="name"
                                           id="name"
                                           class="form-control @error('name') is-invalid @enderror"
                                           value="{{ old('name', $package->name) }}"
                                           placeholder="เช่น แพ็กเกจประหยัด"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="description">รายละเอียดแพ็กเกจ <span class="text-danger">*</span></label>
                                    <textarea name="description"
                                              id="description"
                                              class="form-control @error('description') is-invalid @enderror"
                                              rows="5"
                                              placeholder="อธิบายรายละเอียดของแพ็กเกจ..."
                                              required>{{ old('description', $package->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="price">ราคา (บาท) <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number"
                                               name="price"
                                               id="price"
                                               class="form-control @error('price') is-invalid @enderror"
                                               value="{{ old('price', $package->price) }}"
                                               min="0"
                                               step="1"
                                               placeholder="0"
                                               required>
                                        <div class="input-group-append">
                                            <span class="input-group-text">บาท</span>
                                        </div>
                                        @error('price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="image">รูปภาพประกอบ</label>
                                    @if($package->image)
                                        <div class="mb-2">
                                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($package->image) }}"
                                                 class="img-thumbnail"
                                                 style="width: 300px; height: 200px; object-fit: cover;"
                                                 alt="รูปภาพแพ็กเกจ">
                                            <p class="text-muted mt-1">รูปภาพปัจจุบัน</p>
                                        </div>
                                    @endif
                                    <div class="custom-file">
                                        <input type="file"
                                               name="image"
                                               id="image"
                                               class="custom-file-input @error('image') is-invalid @enderror"
                                               accept="image/*">
                                        <label class="custom-file-label" for="image">เลือกรูปภาพใหม่...</label>
                                        @error('image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB) - เลือกเฉพาะเมื่อต้องการเปลี่ยนรูปภาพ
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>บันทึกการแก้ไข
                                </button>
                                <a href="{{ route('admin.packages.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">ข้อมูลแพ็กเกจ</h3>
                        </div>
                        <div class="card-body">
                            <p><strong>สร้างเมื่อ:</strong> {{ $package->created_at->format('d/m/Y H:i') }}</p>
                            <p><strong>แก้ไขล่าสุด:</strong> {{ $package->updated_at->format('d/m/Y H:i') }}</p>
                            @if($package->image)
                                <p><strong>มีรูปภาพ:</strong> <span class="text-success">ใช่</span></p>
                            @else
                                <p><strong>มีรูปภาพ:</strong> <span class="text-muted">ไม่มี</span></p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Custom file input label update
document.querySelector('.custom-file-input').addEventListener('change', function(e) {
    var fileName = e.target.files[0].name;
    var nextSibling = e.target.nextElementSibling;
    nextSibling.innerText = fileName;
});
</script>
@endsection